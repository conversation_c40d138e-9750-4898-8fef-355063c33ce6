from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app, abort
from flask_login import login_required, current_user
from app.models import (
    Ingredient, Supplier, AdministrativeArea, User, AuditLog,
    StockIn, StockInItem, StockOut, StockOutItem,
    ConsumptionPlan, ConsumptionDetail, Recipe
)
from app.models_ingredient_traceability import MaterialBatch, TraceDocument, BatchFlow
from app import db
from datetime import datetime, date, timedelta
import json
import uuid
import os
from werkzeug.utils import secure_filename
from sqlalchemy import func

traceability_bp = Blueprint('traceability', __name__)

@traceability_bp.route('/traceability')
@login_required
def index():
    """溯源查询首页"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取所有食材和供应商，用于查询
    ingredients = Ingredient.query.all()
    suppliers = Supplier.query.all()
    areas = accessible_areas

    return render_template('traceability/index.html',
                          ingredients=ingredients,
                          suppliers=suppliers,
                          areas=areas)

@traceability_bp.route('/traceability/interface')
@login_required
def interface():
    """溯源接口页面"""
    # 获取查询参数
    trace_type = request.args.get('trace_type', '')
    trace_id = request.args.get('trace_id', type=int)

    # 获取当前用户的学校/区域
    from app.models import AdministrativeArea, User
    user = User.query.get(current_user.id)
    accessible_areas = user.get_accessible_areas()
    current_school_name = "未知学校"

    if accessible_areas and len(accessible_areas) > 0:
        # 使用用户可访问的第一个区域作为当前学校
        current_school_name = accessible_areas[0].name

    # 如果有查询参数，直接显示结果
    if trace_type and trace_id:
        return render_template('traceability/trace_interface_simple.html',
                              trace_type=trace_type,
                              trace_id=trace_id,
                              direct_query=True,
                              current_school_name=current_school_name)

    # 否则显示查询界面
    return render_template('traceability/trace_interface_simple.html',
                          current_school_name=current_school_name)

@traceability_bp.route('/traceability/batch/<batch_number>')
@login_required
def trace_batch(batch_number):
    """通过批次号查询溯源信息"""
    batch = MaterialBatch.query.filter_by(batch_number=batch_number).first_or_404()

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(batch.area_id):
        flash('您没有权限查看该批次', 'danger')
        return redirect(url_for('traceability.index'))

    # 获取批次文档
    documents = TraceDocument.query.filter_by(batch_id=batch.id).all()

    # 获取批次流水
    flows = BatchFlow.query.filter_by(batch_id=batch.id).order_by(BatchFlow.flow_date).all()

    # 获取上游信息（供应商）
    supplier = Supplier.query.get(batch.supplier_id)

    # 获取下游信息（出库记录、消耗计划）
    downstream_info = get_downstream_info(batch.id)

    return render_template('traceability/batch_trace.html',
                          batch=batch,
                          documents=documents,
                          flows=flows,
                          supplier=supplier,
                          downstream_info=downstream_info)

@traceability_bp.route('/traceability/ingredient/<int:id>')
@login_required
def trace_ingredient(id):
    """通过食材ID查询溯源信息"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()
    area_ids = [area.id for area in accessible_areas]

    # 获取食材信息
    ingredient = Ingredient.query.get_or_404(id)

    # 获取该食材的所有批次
    batches = MaterialBatch.query.filter(
        MaterialBatch.ingredient_id == id,
        MaterialBatch.area_id.in_(area_ids)
    ).order_by(MaterialBatch.created_at.desc()).all()

    return render_template('traceability/ingredient_trace.html',
                          ingredient=ingredient,
                          batches=batches)

@traceability_bp.route('/traceability/menu/<int:id>')
@login_required
def trace_menu(id):
    """通过菜单计划ID查询溯源信息 - 日菜单功能已移除"""
    # 日菜单功能已移除，返回404
    abort(404)

    # 检查用户是否有权限查看
    if not current_user.can_access_area_by_id(menu_plan.area_id):
        flash('您没有权限查看该菜单计划', 'danger')
        return redirect(url_for('traceability.index'))

    # 获取菜单中的食谱
    menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=id).all()

    # 获取消耗计划
    consumption_plan = ConsumptionPlan.query.filter_by(menu_plan_id=id).first()

    # 如果存在消耗计划，获取相关的批次信息
    batch_info = []
    if consumption_plan:
        # 获取消耗明细
        consumption_details = ConsumptionDetail.query.filter_by(consumption_plan_id=consumption_plan.id).all()

        # 获取出库单
        stock_outs = StockOut.query.filter_by(consumption_plan_id=consumption_plan.id).all()

        # 获取出库明细和相关批次
        for stock_out in stock_outs:
            stock_out_items = StockOutItem.query.filter_by(stock_out_id=stock_out.id).all()
            for item in stock_out_items:
                if item.batch_number:
                    batch = MaterialBatch.query.filter_by(batch_number=item.batch_number).first()
                    if batch:
                        batch_info.append({
                            'batch': batch,
                            'quantity': item.quantity,
                            'unit': item.unit,
                            'ingredient_name': item.ingredient.name if item.ingredient else '未知食材'
                        })

    return render_template('traceability/menu_trace.html',
                          menu_plan=menu_plan,
                          menu_recipes=menu_recipes,
                          consumption_plan=consumption_plan,
                          batch_info=batch_info)

@traceability_bp.route('/traceability/search', methods=['POST'])
@login_required
def search():
    """溯源搜索"""
    search_type = request.form.get('search_type')
    search_value = request.form.get('search_value')

    if not search_value:
        flash('请输入搜索内容', 'warning')
        return redirect(url_for('traceability.index'))

    if search_type == 'batch_number':
        batch = MaterialBatch.query.filter_by(batch_number=search_value).first()
        if batch:
            return redirect(url_for('traceability.trace_batch', batch_number=batch.batch_number))
        else:
            flash('未找到该批次号', 'warning')
            return redirect(url_for('traceability.index'))

    elif search_type == 'ingredient_id':
        try:
            ingredient_id = int(search_value)
            ingredient = Ingredient.query.get(ingredient_id)
            if ingredient:
                return redirect(url_for('traceability.trace_ingredient', id=ingredient_id))
            else:
                flash('未找到该食材', 'warning')
                return redirect(url_for('traceability.index'))
        except ValueError:
            flash('食材ID必须是数字', 'warning')
            return redirect(url_for('traceability.index'))

    elif search_type == 'menu_id':
        try:
            menu_id = int(search_value)
            menu_plan = MenuPlan.query.get(menu_id)
            if menu_plan:
                return redirect(url_for('traceability.trace_menu', id=menu_id))
            else:
                flash('未找到该菜单计划', 'warning')
                return redirect(url_for('traceability.index'))
        except ValueError:
            flash('菜单计划ID必须是数字', 'warning')
            return redirect(url_for('traceability.index'))

    flash('无效的搜索类型', 'warning')
    return redirect(url_for('traceability.index'))

# 辅助函数
def get_downstream_info(batch_id):
    """获取批次的下游信息"""
    batch = MaterialBatch.query.get(batch_id)
    if not batch:
        return None

    # 查找与该批次相关的出库记录
    stock_out_items = StockOutItem.query.filter_by(batch_number=batch.batch_number).all()

    downstream_info = []
    for item in stock_out_items:
        stock_out = StockOut.query.get(item.stock_out_id)
        if stock_out and stock_out.consumption_plan_id:
            consumption_plan = ConsumptionPlan.query.get(stock_out.consumption_plan_id)
            if consumption_plan:
                menu_plan = MenuPlan.query.get(consumption_plan.menu_plan_id)
                if menu_plan:
                    downstream_info.append({
                        'stock_out': stock_out,
                        'stock_out_item': item,
                        'consumption_plan': consumption_plan,
                        'menu_plan': menu_plan
                    })

    return downstream_info
