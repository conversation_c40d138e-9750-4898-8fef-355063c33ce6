from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify, current_app
from flask_login import login_required, current_user
from app.models import (
    ConsumptionPlan, ConsumptionDetail, Ingredient, Inventory, InventoryAlert,
    AdministrativeArea, Warehouse, IngredientCategory
)
from app import db
from datetime import datetime, date
import json

consumption_plan_super_bp = Blueprint('consumption_plan_super', __name__)

@consumption_plan_super_bp.route('/consumption-plan/super-editor')
@login_required
def super_editor():
    """消耗计划超级编辑器"""
    # 获取当前用户可访问的区域
    accessible_areas = current_user.get_accessible_areas()

    # 获取当前用户的默认区域
    current_area = current_user.get_current_area()

    # 获取默认仓库（如果有当前区域）
    warehouse = None
    if current_area:
        warehouses = Warehouse.query.filter_by(area_id=current_area.id, status='正常').first()
        warehouse = warehouses

    return render_template('consumption_plan/super_editor.html',
                          areas=accessible_areas,
                          current_area=current_area,
                          warehouse=warehouse,
                          today_date=date.today().strftime('%Y-%m-%d'))

@consumption_plan_super_bp.route('/consumption-plan/get-warehouses/<int:area_id>')
@login_required
def get_warehouses(area_id):
    """根据区域获取仓库列表"""
    try:
        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            return jsonify({'error': '您没有权限操作该区域'}), 403

        # 获取区域信息
        area = AdministrativeArea.query.get(area_id)
        if not area:
            return jsonify({
                'error': f'未找到ID为{area_id}的区域',
                'message': '请选择有效的区域'
            }), 404

        area_name = area.name

        # 获取该区域的所有仓库
        warehouses = Warehouse.query.filter_by(area_id=area_id, status='正常').all()

        # 转换为JSON格式
        warehouses_data = []
        for warehouse in warehouses:
            warehouses_data.append({
                'id': warehouse.id,
                'name': warehouse.name,
                'location': warehouse.location,
                'manager_id': warehouse.manager_id,
                'status': warehouse.status
            })

        return jsonify({
            'success': True,
            'area_id': area_id,
            'area_name': area_name,
            'warehouses': warehouses_data
        })

    except Exception as e:
        # 记录错误并返回友好的错误信息
        current_app.logger.error(f"获取仓库列表失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': '获取仓库列表失败',
            'message': str(e)
        }), 500

def safe_get_int_param(param_name, default=None):
    """安全地获取整数参数，如果转换失败返回默认值"""
    try:
        value = request.args.get(param_name)
        if value is None or value == '':
            return default
        return int(value)
    except (ValueError, TypeError):
        current_app.logger.warning(f"无效的{param_name}参数: {request.args.get(param_name)}")
        return default

@consumption_plan_super_bp.route('/consumption-plan/get_inventory_batches')
@login_required
def get_inventory_batches():
    """获取仓库中的库存批次（按FIFO排序）"""
    warehouse_id = safe_get_int_param('warehouse_id')

    if not warehouse_id:
        return jsonify({'error': '缺少仓库ID参数'}), 400

    # 检查仓库是否存在
    warehouse = Warehouse.query.get(warehouse_id)
    if not warehouse:
        return jsonify({'error': '仓库不存在'}), 404

    # 检查用户是否有权限访问该仓库
    if not current_user.can_access_area_by_id(warehouse.area_id):
        return jsonify({'error': '您没有权限访问该仓库'}), 403

    try:
        # 使用原始SQL查询避免数据类型转换问题
        from sqlalchemy import text

        sql = text("""
            SELECT
                i.id, i.ingredient_id, i.batch_number, i.supplier_id,
                i.production_date, i.expiry_date, i.quantity, i.unit,
                i.storage_location_id,
                ing.name as ingredient_name,
                cat.name as category_name,
                sup.name as supplier_name,
                sl.name as storage_location_name,
                COALESCE(si.unit_price, 0.0) as unit_price
            FROM inventories i
            JOIN ingredients ing ON i.ingredient_id = ing.id
            LEFT JOIN ingredient_categories cat ON ing.category_id = cat.id
            LEFT JOIN suppliers sup ON i.supplier_id = sup.id
            LEFT JOIN storage_locations sl ON i.storage_location_id = sl.id
            LEFT JOIN stock_in_items si ON i.batch_number = si.batch_number
                AND i.ingredient_id = si.ingredient_id
                AND i.supplier_id = si.supplier_id
            WHERE i.warehouse_id = :warehouse_id
            AND i.status = :status
            AND CAST(i.quantity AS DECIMAL(18, 2)) > :min_quantity
            ORDER BY i.production_date
        """)

        inventory_result = db.session.execute(sql, {
            'warehouse_id': warehouse_id,
            'status': '正常',
            'min_quantity': 0.0
        }).fetchall()

        # 转换为JSON格式
        result = []
        for batch in inventory_result:
            # 安全地处理日期字段
            production_date = None
            if batch.production_date:
                if hasattr(batch.production_date, 'strftime'):
                    production_date = batch.production_date.strftime('%Y-%m-%d')
                else:
                    production_date = str(batch.production_date)

            expiry_date = None
            if batch.expiry_date:
                if hasattr(batch.expiry_date, 'strftime'):
                    expiry_date = batch.expiry_date.strftime('%Y-%m-%d')
                else:
                    expiry_date = str(batch.expiry_date)

            result.append({
                'id': batch.id,
                'ingredient_id': batch.ingredient_id,
                'ingredient_name': batch.ingredient_name,
                'category': batch.category_name or '未分类',
                'batch_number': batch.batch_number,
                'supplier_id': batch.supplier_id,
                'supplier_name': batch.supplier_name,
                'production_date': production_date,
                'expiry_date': expiry_date,
                'quantity': float(batch.quantity),
                'unit': batch.unit,
                'unit_price': float(batch.unit_price) if batch.unit_price else 0.0,
                'storage_location_id': batch.storage_location_id,
                'storage_location_name': batch.storage_location_name
            })

        return jsonify(result)

    except Exception as e:
        current_app.logger.error(f"获取库存批次时出错: {str(e)}")
        return jsonify({'error': '获取库存批次时出错', 'message': str(e)}), 500

@consumption_plan_super_bp.route('/consumption-plan/create_super', methods=['POST'])
@login_required
def create_super():
    """使用超级编辑器创建消耗计划"""
    try:
        # 获取表单数据
        area_id = request.form.get('area_id', type=int)
        warehouse_id = request.form.get('warehouse_id', type=int)
        consumption_date = request.form.get('consumption_date')
        meal_types = request.form.getlist('meal_types[]')  # 获取多个餐次
        diners_count = request.form.get('diners_count', type=int)
        notes = request.form.get('notes')

        # 调试信息
        current_app.logger.info(f"超级编辑器接收到的数据:")
        current_app.logger.info(f"  area_id: {area_id}")
        current_app.logger.info(f"  warehouse_id: {warehouse_id}")
        current_app.logger.info(f"  consumption_date: {consumption_date}")
        current_app.logger.info(f"  meal_types: {meal_types}")
        current_app.logger.info(f"  diners_count: {diners_count}")
        current_app.logger.info(f"  notes: {notes}")

        # 验证必要参数
        if not all([area_id, warehouse_id, consumption_date]) or not meal_types:
            flash('请填写所有必要信息（包括至少选择一个餐次）', 'danger')
            return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

        # 检查用户是否有权限操作该区域
        if not current_user.can_access_area_by_id(area_id):
            flash('您没有权限操作该区域', 'danger')
            return redirect('http://127.0.0.1:5000/consumption-plan')

        # 获取选中的批次
        selected_batches = request.form.getlist('selected_batches[]')

        if not selected_batches:
            flash('请至少选择一个批次进行消耗', 'warning')
            return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

        # 使用原始 SQL 创建消耗计划，避免 SQLAlchemy ORM 的时间戳处理问题
        from sqlalchemy import text

        # 将日期字符串转换为 date 对象
        consumption_date_obj = datetime.strptime(consumption_date, '%Y-%m-%d').date()

        # 将多个餐次合并为一个字符串
        meal_type_combined = '+'.join(meal_types)
        current_app.logger.info(f"创建包含餐次 {meal_type_combined} 的消耗计划")

        # 日菜单功能已移除，不再关联菜单计划
        menu_plan_id = None
        current_app.logger.info(f"日菜单功能已移除，将创建独立的消耗计划: area_id={area_id}, date={consumption_date_obj}, meal_types={meal_types}")

        # 完全避免使用参数绑定，直接构建 SQL 语句
        # 将日期格式化为 SQL Server 可接受的格式
        formatted_date = consumption_date_obj.strftime('%Y-%m-%d')
        diners_count_value = diners_count or 1
        # 处理字符串中的单引号，避免 SQL 注入
        meal_type_safe = meal_type_combined.replace("'", "''") if meal_type_combined else ''
        notes_value = (notes or '').replace("'", "''")

        sql = text(f"""
            INSERT INTO consumption_plans
            (menu_plan_id, area_id, consumption_date, meal_type, diners_count, status, created_by, notes, created_at, updated_at)
            OUTPUT inserted.id
            VALUES
            ({menu_plan_id if menu_plan_id else 'NULL'}, {area_id}, '{formatted_date}', '{meal_type_safe}', {diners_count_value}, '计划中', {current_user.id}, '{notes_value}',
            GETDATE(), GETDATE())
        """)

        result = db.session.execute(sql)
        consumption_plan_id = result.fetchone()[0]

        current_app.logger.info(f"创建了消耗计划 ID: {consumption_plan_id} (餐次: {meal_type_combined})")

        # 添加消耗明细到创建的消耗计划
        for batch_id in selected_batches:
            quantity = request.form.get(f'quantity_{batch_id}', type=float)

            if quantity and quantity > 0:
                # 获取批次信息
                batch = Inventory.query.get(batch_id)

                if batch:
                    # 验证消耗数量不超过库存数量
                    if quantity > batch.quantity:
                        flash(f'食材 {batch.ingredient.name} (批次号: {batch.batch_number}) 的消耗数量不能超过库存数量 {batch.quantity} {batch.unit}', 'danger')
                        db.session.rollback()
                        return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

                    # 使用直接构建的 SQL 语句创建消耗明细
                    # 处理字符串中的单引号，避免 SQL 注入
                    unit_safe = batch.unit.replace("'", "''") if batch.unit else ''
                    batch_number_safe = batch.batch_number.replace("'", "''") if batch.batch_number else ''

                    # 将批次信息存储在 notes 字段中
                    notes_value = f'从批次 {batch_number_safe} 创建，库存ID: {batch.id}'
                    notes_safe = notes_value.replace("'", "''")

                    detail_sql = text(f"""
                        INSERT INTO consumption_details
                        (consumption_plan_id, ingredient_id, inventory_id, batch_number, planned_quantity, unit, status, is_main_ingredient, notes, created_at, updated_at)
                        VALUES
                        ({consumption_plan_id}, {batch.ingredient_id}, {batch.id}, '{batch_number_safe}', {quantity}, '{unit_safe}', '待出库', 1, '{notes_safe}', GETDATE(), GETDATE())
                    """)

                    db.session.execute(detail_sql)

        db.session.commit()

        # 检查库存是否足够
        from app.routes.consumption_plan import check_inventory
        check_inventory(consumption_plan_id, area_id, warehouse_id)

        # 成功消息
        if len(meal_types) == 1:
            flash('消耗计划创建成功', 'success')
        else:
            flash(f'消耗计划创建成功（包含餐次：{", ".join(meal_types)}）', 'success')

        return redirect('http://127.0.0.1:5000/consumption-plan')

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"创建消耗计划时出错: {str(e)}")
        flash(f'创建消耗计划时出错: {str(e)}', 'danger')
        return redirect('http://127.0.0.1:5000/consumption-plan/super-editor')

@consumption_plan_super_bp.route('/consumption-plan/analyze-recipes', methods=['POST'])
@login_required
def analyze_recipes():
    """分析食谱和食材需求 - 完全使用ORM避免SQL参数绑定问题"""
    try:
        data = request.get_json()
        current_app.logger.info(f"收到食谱分析请求: {data}")

        area_id = data.get('area_id')
        consumption_date = data.get('consumption_date')
        meal_types = data.get('meal_types', [])

        if not all([area_id, consumption_date, meal_types]):
            return jsonify({'error': '缺少必要参数'}), 400

        # 检查用户权限
        if not current_user.can_access_area_by_id(area_id):
            return jsonify({'error': '您没有权限访问该区域'}), 403

        from datetime import datetime
        from app.models import Recipe, WeeklyMenu, WeeklyMenuRecipe

        # 转换日期格式
        consumption_date_obj = datetime.strptime(consumption_date, '%Y-%m-%d').date()
        current_app.logger.info(f"查询日期: {consumption_date_obj}, 区域: {area_id}, 餐次: {meal_types}")

        recipes = []

        for meal_type in meal_types:
            current_app.logger.info(f"查询餐次: {meal_type}")

            # 日菜单功能已移除，改为从周菜单获取食谱
            current_app.logger.info(f"日菜单功能已移除，从周菜单获取食谱")

            # 计算星期几
            day_of_week = consumption_date_obj.weekday() + 1  # 1-7表示周一到周日

            # 查找包含该日期的周菜单
            weekly_menu = WeeklyMenu.query.filter(
                WeeklyMenu.area_id == area_id,
                WeeklyMenu.week_start <= consumption_date_obj,
                WeeklyMenu.week_end >= consumption_date_obj,
                WeeklyMenu.status == '已发布'
            ).first()

            if weekly_menu:
                # 获取该餐次的食谱
                weekly_recipes = WeeklyMenuRecipe.query.filter(
                    WeeklyMenuRecipe.weekly_menu_id == weekly_menu.id,
                    WeeklyMenuRecipe.day_of_week == day_of_week,
                    WeeklyMenuRecipe.meal_type == meal_type
                ).all()

                for wr in weekly_recipes:
                    if wr.recipe:
                            # 分析食谱的食材需求
                            main_ingredients = []
                            recipe_ingredients = wr.recipe.ingredients.all()

                            for ri in recipe_ingredients:
                                if ri.ingredient:
                                    # 查询库存
                                    from app.models import Inventory, Warehouse
                                    inventory_records = db.session.query(Inventory).join(Warehouse).filter(
                                        Inventory.ingredient_id == ri.ingredient_id,
                                        Warehouse.area_id == area_id,
                                        Inventory.status == '正常'
                                    ).all()

                                    # 计算总库存
                                    total_quantity = 0
                                    for inv in inventory_records:
                                        try:
                                            qty = float(inv.quantity) if inv.quantity else 0
                                            if qty > 0:
                                                total_quantity += qty
                                        except (ValueError, TypeError):
                                            continue

                                    available_quantity = float(total_quantity or 0)
                                    required_quantity = float(ri.quantity or 0)
                                    is_available = available_quantity >= required_quantity

                                    main_ingredients.append({
                                        'name': ri.ingredient.name,
                                        'required_quantity': required_quantity,
                                        'available_quantity': available_quantity,
                                        'unit': ri.unit or '',
                                        'available': is_available,
                                        'ingredient_id': ri.ingredient_id
                                    })

                            recipes.append({
                                'recipe_id': wr.recipe.id,
                                'recipe_name': wr.recipe.name,
                                'meal_type': meal_type,
                                'expected_diners': 100,  # 默认值
                                'main_ingredients': main_ingredients
                            })
            else:
                # 没有找到周菜单
                current_app.logger.info("未找到周菜单")

                # 没有找到任何菜单计划
                recipes.append({
                    'recipe_id': None,
                    'recipe_name': '暂无安排',
                    'meal_type': meal_type,
                    'expected_diners': None,
                    'main_ingredients': []
                })

        # 汇总缺失的食材
        missing_ingredients = []
        missing_summary = {}

        for recipe in recipes:
            if recipe.get('main_ingredients'):
                for ingredient in recipe['main_ingredients']:
                    if not ingredient.get('available', True):
                        key = ingredient['ingredient_id']
                        shortage = ingredient['required_quantity'] - ingredient['available_quantity']

                        if key not in missing_summary:
                            missing_summary[key] = {
                                'name': ingredient['name'],
                                'total_shortage': 0,
                                'unit': ingredient['unit'],
                                'details': []
                            }

                        missing_summary[key]['total_shortage'] += shortage
                        missing_summary[key]['details'].append({
                            'meal_type': recipe['meal_type'],
                            'recipe_name': recipe['recipe_name'],
                            'shortage': shortage
                        })

        missing_ingredients = list(missing_summary.values())

        return jsonify({
            'recipes': recipes,
            'missing_ingredients': missing_ingredients,
            'total_missing_count': len(missing_ingredients)
        })

    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        current_app.logger.error(f"分析食谱时出错: {str(e)}")
        current_app.logger.error(f"错误详情: {error_details}")
        return jsonify({
            'error': '分析食谱时出错',
            'message': str(e)
        }), 500





